import React, { createContext, useContext, useEffect, useState } from 'react'
import { 
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  sendPasswordResetEmail,
  updateProfile,
  GoogleAuthProvider,
  signInWithPopup
} from 'firebase/auth'
import { auth } from '../config/firebase'
import { apiHelpers, endpoints } from '../config/api'
import toast from 'react-hot-toast'

const AuthContext = createContext({})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [userProfile, setUserProfile] = useState(null)
  const [loading, setLoading] = useState(true)
  const [initializing, setInitializing] = useState(true)

  // Google Auth Provider
  const googleProvider = new GoogleAuthProvider()

  // Register new user
  const register = async (userData) => {
    try {
      setLoading(true)
      
      // Create Firebase user
      const { user: firebaseUser } = await createUserWithEmailAndPassword(
        auth,
        userData.email,
        userData.password
      )

      // Update Firebase profile
      await updateProfile(firebaseUser, {
        displayName: `${userData.firstName} ${userData.lastName}`,
      })

      // Register user in backend
      const profileData = {
        firebase_uid: firebaseUser.uid,
        email: userData.email,
        first_name: userData.firstName,
        last_name: userData.lastName,
        phone: userData.phone,
        user_type: 'customer'
      }

      const response = await apiHelpers.post(endpoints.auth.register, profileData)
      
      if (response.success) {
        setUserProfile(response.data)
        toast.success('Account created successfully!')
        return { success: true, user: firebaseUser }
      } else {
        throw new Error(response.message || 'Registration failed')
      }
    } catch (error) {
      console.error('Registration error:', error)
      
      // If backend registration fails, delete Firebase user
      if (auth.currentUser) {
        await auth.currentUser.delete()
      }
      
      const errorMessage = error.message || 'Registration failed. Please try again.'
      toast.error(errorMessage)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // Login user
  const login = async (email, password) => {
    try {
      setLoading(true)
      
      // Sign in with Firebase
      const { user: firebaseUser } = await signInWithEmailAndPassword(auth, email, password)
      
      // Login to backend
      const response = await apiHelpers.post(endpoints.auth.login, {
        firebase_uid: firebaseUser.uid
      })
      
      if (response.success) {
        setUserProfile(response.data)
        toast.success('Welcome back!')
        return { success: true, user: firebaseUser }
      } else {
        throw new Error(response.message || 'Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      const errorMessage = error.message || 'Login failed. Please check your credentials.'
      toast.error(errorMessage)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // Login with Google
  const loginWithGoogle = async () => {
    try {
      setLoading(true)
      
      const { user: firebaseUser } = await signInWithPopup(auth, googleProvider)
      
      // Check if user exists in backend, if not register them
      try {
        const response = await apiHelpers.post(endpoints.auth.login, {
          firebase_uid: firebaseUser.uid
        })
        
        if (response.success) {
          setUserProfile(response.data)
          toast.success('Welcome back!')
        }
      } catch (loginError) {
        // If login fails, try to register
        if (loginError.status === 404) {
          const names = firebaseUser.displayName?.split(' ') || ['', '']
          const profileData = {
            firebase_uid: firebaseUser.uid,
            email: firebaseUser.email,
            first_name: names[0] || '',
            last_name: names.slice(1).join(' ') || '',
            phone: firebaseUser.phoneNumber || '',
            user_type: 'customer'
          }

          const registerResponse = await apiHelpers.post(endpoints.auth.register, profileData)
          
          if (registerResponse.success) {
            setUserProfile(registerResponse.data)
            toast.success('Account created successfully!')
          }
        } else {
          throw loginError
        }
      }
      
      return { success: true, user: firebaseUser }
    } catch (error) {
      console.error('Google login error:', error)
      const errorMessage = error.message || 'Google login failed. Please try again.'
      toast.error(errorMessage)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // Logout user
  const logout = async () => {
    try {
      setLoading(true)
      
      // Logout from backend
      try {
        await apiHelpers.post(endpoints.auth.logout)
      } catch (error) {
        console.warn('Backend logout failed:', error)
      }
      
      // Sign out from Firebase
      await signOut(auth)
      
      setUser(null)
      setUserProfile(null)
      toast.success('Logged out successfully')
    } catch (error) {
      console.error('Logout error:', error)
      toast.error('Logout failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Reset password
  const resetPassword = async (email) => {
    try {
      await sendPasswordResetEmail(auth, email)
      toast.success('Password reset email sent!')
      return { success: true }
    } catch (error) {
      console.error('Password reset error:', error)
      const errorMessage = error.message || 'Failed to send password reset email.'
      toast.error(errorMessage)
      throw error
    }
  }

  // Update user profile
  const updateUserProfile = async (profileData) => {
    try {
      setLoading(true)
      
      const response = await apiHelpers.put(endpoints.auth.profile, profileData)
      
      if (response.success) {
        setUserProfile(response.data)
        
        // Update Firebase profile if name changed
        if (profileData.first_name || profileData.last_name) {
          await updateProfile(auth.currentUser, {
            displayName: `${profileData.first_name || userProfile.first_name} ${profileData.last_name || userProfile.last_name}`,
          })
        }
        
        toast.success('Profile updated successfully!')
        return response.data
      } else {
        throw new Error(response.message || 'Profile update failed')
      }
    } catch (error) {
      console.error('Profile update error:', error)
      const errorMessage = error.message || 'Failed to update profile.'
      toast.error(errorMessage)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // Get user profile from backend
  const fetchUserProfile = async () => {
    try {
      const response = await apiHelpers.get(endpoints.auth.profile)
      
      if (response.success) {
        setUserProfile(response.data)
        return response.data
      }
    } catch (error) {
      console.error('Fetch profile error:', error)
      // Don't show error toast here as it might be called on app init
    }
  }

  // Listen for authentication state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      try {
        setUser(firebaseUser)
        
        if (firebaseUser) {
          // User is signed in, fetch profile from backend
          await fetchUserProfile()
        } else {
          // User is signed out
          setUserProfile(null)
        }
      } catch (error) {
        console.error('Auth state change error:', error)
      } finally {
        setLoading(false)
        setInitializing(false)
      }
    })

    return unsubscribe
  }, [])

  const value = {
    // State
    user,
    userProfile,
    loading,
    initializing,
    
    // Methods
    register,
    login,
    loginWithGoogle,
    logout,
    resetPassword,
    updateUserProfile,
    fetchUserProfile,
    
    // Computed values
    isAuthenticated: !!user,
    isCustomer: userProfile?.user_type === 'customer',
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
