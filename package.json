{"name": "work-boy-booking", "version": "1.0.0", "description": "A comprehensive platform connecting busy professionals with trusted individuals for personal tasks and errands", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:web\" \"npm run dev:admin\"", "dev:backend": "cd backend && php spark serve", "dev:web": "cd web-app && npm start", "dev:admin": "cd admin-panel && npm start", "dev:customer-mobile": "cd mobile-app/customer-app && npx react-native start", "dev:workboy-mobile": "cd mobile-app/workboy-app && npx react-native start", "install:all": "npm run install:web && npm run install:admin && npm run install:mobile", "install:web": "cd web-app && npm install", "install:admin": "cd admin-panel && npm install", "install:mobile": "cd mobile-app/customer-app && npm install && cd ../workboy-app && npm install", "build": "npm run build:web && npm run build:admin", "build:web": "cd web-app && npm run build", "build:admin": "cd admin-panel && npm run build", "test": "npm run test:web && npm run test:admin", "test:web": "cd web-app && npm test", "test:admin": "cd admin-panel && npm test"}, "keywords": ["booking", "services", "mobile-app", "web-app", "react-native", "react", "php", "codeigniter"], "author": "<PERSON><PERSON>", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["web-app", "admin-panel", "mobile-app/customer-app", "mobile-app/workboy-app"]}