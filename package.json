{"name": "work-boy-booking", "version": "1.0.0", "description": "A comprehensive platform connecting busy professionals with trusted individuals for personal tasks and errands", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:web\" \"npm run dev:admin\"", "dev:backend": "cd backend && I:\\xampp\\php\\php.exe spark serve", "dev:web": "cd customer-web && npm run dev", "dev:admin": "cd admin-panel && npm run dev", "dev:customer-mobile": "cd mobile-app/customer-app && npx react-native start", "dev:workboy-mobile": "cd mobile-app/workboy-app && npx react-native start", "install:all": "npm run install:web && npm run install:admin && npm run install:mobile", "install:web": "cd customer-web && npm install", "install:admin": "cd admin-panel && npm install", "install:mobile": "cd mobile-app/customer-app && npm install && cd ../workboy-app && npm install", "build": "npm run build:web && npm run build:admin", "build:web": "cd customer-web && npm run build", "build:admin": "cd admin-panel && npm run build", "test": "npm run test:web && npm run test:admin", "test:web": "cd customer-web && npm test", "test:admin": "cd admin-panel && npm test"}, "keywords": ["booking", "services", "mobile-app", "web-app", "react-native", "react", "php", "codeigniter"], "author": "<PERSON><PERSON>", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "_workspaces_disabled": ["customer-web", "admin-panel", "customer-mobile", "workboy-mobile"]}